import pandas as pd
import os
import subprocess
import tempfile
from typing import Dict, Any
from dotenv import load_dotenv

def test_flawfinder_on_file(file_path: str, repo_dir: str) -> Dict[str, Any]:
    """Test flawfinder on a specific file."""
    full_path = os.path.join(repo_dir, file_path)
    
    if not os.path.exists(full_path):
        return {
            "success": False,
            "error": f"File not found: {full_path}",
            "output": "",
            "vulnerabilities_found": 0
        }
    
    try:
        # Run flawfinder on the specific file
        result = subprocess.run(
            ['flawfinder', '--quiet', '--dataonly', full_path],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        output = result.stdout + result.stderr
        
        # Count vulnerabilities (flawfinder outputs one line per vulnerability when using --dataonly)
        vuln_count = len([line for line in output.split('\n') if line.strip() and not line.startswith('Flawfinder')])
        
        return {
            "success": True,
            "error": None,
            "output": output,
            "vulnerabilities_found": vuln_count,
            "exit_code": result.returncode
        }
        
    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "error": "Flawfinder timeout",
            "output": "",
            "vulnerabilities_found": 0
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "output": "",
            "vulnerabilities_found": 0
        }

def main():
    load_dotenv()
    
    # Load the CSV file
    df = pd.read_csv("mh_sf_reposvul.csv")
    
    # Get unique CVEs to avoid duplicates
    unique_cves = df.drop_duplicates(subset=['cve_id'])
    
    results = []
    total_files = 0
    files_with_vulns = 0
    files_not_found = 0
    
    print("Testing flawfinder on CVE files...")
    print("-" * 50)
    
    for _, row in unique_cves.iterrows():
        cve_id = row["cve_id"]
        file_name = row["file_name"]
        repo_dir = f"../mh_sf_cve_repos/{cve_id}"
        
        total_files += 1
        
        # Test flawfinder on this file
        result = test_flawfinder_on_file(file_name, repo_dir)
        
        if not result["success"]:
            if "File not found" in result["error"]:
                files_not_found += 1
            print(f"❌ {cve_id}: {result['error']}")
        else:
            vuln_count = result["vulnerabilities_found"]
            if vuln_count > 0:
                files_with_vulns += 1
                print(f"🔍 {cve_id}: Found {vuln_count} vulnerabilities in {file_name}")
            else:
                print(f"✅ {cve_id}: No vulnerabilities detected in {file_name}")
        
        results.append({
            "cve_id": cve_id,
            "file_name": file_name,
            "repo_dir": repo_dir,
            **result
        })
    
    print("-" * 50)
    print(f"Summary:")
    print(f"Total files tested: {total_files}")
    print(f"Files with vulnerabilities detected: {files_with_vulns}")
    print(f"Files not found: {files_not_found}")
    print(f"Success rate: {((total_files - files_not_found) / total_files * 100):.1f}%")
    
    # Show some examples of vulnerabilities found
    vuln_examples = [r for r in results if r["success"] and r["vulnerabilities_found"] > 0][:5]
    if vuln_examples:
        print(f"\nExample vulnerabilities found:")
        for example in vuln_examples:
            print(f"\n{example['cve_id']} ({example['file_name']}):")
            print(example['output'][:200] + "..." if len(example['output']) > 200 else example['output'])

if __name__ == "__main__":
    main()