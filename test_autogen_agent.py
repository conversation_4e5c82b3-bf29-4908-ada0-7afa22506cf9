#!/usr/bin/env python3
"""
Example script demonstrating how to use the AutoGen-based repair agent.
"""

from utils.autogen_repair_agent import AutoGenRepairAgent
import os
from dotenv import load_dotenv
import signal
import pandas as pd
from tqdm import tqdm

class TimeoutException(Exception):
    pass

def handler(signum, frame):
    raise TimeoutException("Timeout: repair_cve took more than 10 minutes")

def run_autogen_pipeline(row):
    """Run the AutoGen repair pipeline for a single CVE."""
    try:
        cve_id = row["cve_id"]
        commit_hash = row["commit_id"]
        repo_url = row["project"]
        repo_dir = f"../mh_sf_cve_repos/{cve_id}"
        cve_desc = row["cve_description"]
        
        # Set timeout
        signal.alarm(600)  # 10 minutes timeout
        
        # Create AutoGen repair agent
        agent = AutoGenRepairAgent(
            vuln_path=row["file_name"],
            host_repo_dir=repo_dir,
            cve_description=cve_desc,
            tools_allowed=['flawfinder'],  # Tools allowed for this agent
            model_choice='openai',
            model_name=os.getenv('GEMINI_MODEL'),
            client_api_key=os.getenv('GEMINI_API_KEY'),
            base_url=os.getenv('GEMINI_BASE_URL'),
        )
        
        # Setup environment
        print(f"Setting up environment for {cve_id}...")
        agent.setup_environment()
        
        # Run the repair conversation
        print(f"Starting repair conversation for {cve_id}...")
        chat_result = agent.run_repair_conversation()
        
        # Extract patch
        print(f"Extracting patch for {cve_id}...")
        agent.extract_patch(cve_id, "autogen_cves_patches")
        
        # Cleanup
        agent.cleanup()
        
        print(f"Successfully processed {cve_id}")
        return True

    except TimeoutException as te:
        print("Timeout:", f"{cve_id} - {str(te)}")
        return False
    except Exception as e:
        print("Error processing:", f"{cve_id} - {str(e)}")
        return False
    finally:
        signal.alarm(0)  # Cancel the alarm

def run_single_example():
    """Run a single example for testing."""
    load_dotenv()
    
    # Example CVE data - replace with actual data
    example_cve = {
        "cve_id": "CVE-2023-EXAMPLE",
        "commit_id": "abc123",
        "project": "https://github.com/example/repo",
        "file_name": "src/vulnerable.c",
        "cve_description": "Buffer overflow vulnerability in the parse_input function allows remote code execution."
    }
    
    try:
        agent = AutoGenRepairAgent(
            vuln_path=example_cve["file_name"],
            host_repo_dir="./example_repo",  # Adjust path as needed
            cve_description=example_cve["cve_description"],
            tools_allowed=['flawfinder'],
            model_choice='openai',
            model_name=os.getenv('GEMINI_MODEL'),
            client_api_key=os.getenv('GEMINI_API_KEY'),
            base_url=os.getenv('GEMINI_BASE_URL'),
        )
        
        print("Setting up environment...")
        agent.setup_environment()
        
        print("Starting repair conversation...")
        chat_result = agent.run_repair_conversation()
        
        print("Conversation completed. Chat result:")
        print(f"Number of messages: {len(chat_result.chat_history) if hasattr(chat_result, 'chat_history') else 'N/A'}")
        
        print("Extracting patch...")
        patch_path = agent.extract_patch(example_cve["cve_id"], "autogen_patches")
        print(f"Patch saved to: {patch_path}")
        
        print("Cleaning up...")
        agent.cleanup()
        
        print("Example completed successfully!")
        
    except Exception as e:
        print(f"Error in example: {e}")
        try:
            agent.cleanup()
        except:
            pass

def run_batch_processing():
    """Run batch processing on a CSV file of CVEs."""
    load_dotenv()
    signal.signal(signal.SIGALRM, handler)

    # Load the CSV file into a DataFrame
    try:
        test_df = pd.read_csv("mh_sf_reposvul.csv")
    except FileNotFoundError:
        print("CSV file 'mh_sf_reposvul.csv' not found. Please ensure the file exists.")
        return

    failed_count = 0

    # Get unique CVE IDs to avoid duplicate processing
    unique_cves = test_df.drop_duplicates(subset=['cve_id'])
    progress_bar = tqdm(unique_cves.iterrows(), total=len(unique_cves), desc="Processing CVEs with AutoGen")

    for _, row in progress_bar:
        cve_id = row["cve_id"]
        success = run_autogen_pipeline(row)
        if not success:
            failed_count += 1
        progress_bar.set_postfix(failed=failed_count)

    print(f"\nBatch processing completed. Failed: {failed_count}/{len(unique_cves)}")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "batch":
        print("Running batch processing...")
        run_batch_processing()
    else:
        print("Running single example...")
        print("Use 'python run-autogen-agent.py batch' to run batch processing.")
        run_single_example()
