from utils.autogen_repair_agent import AutoGenRepairAgent
import ast
from utils.cve_database import CVEDatabase
from utils.cve_pipeline import CVEBenchPipeline
import json
import os
from tqdm import tqdm 
import signal
from dotenv import load_dotenv
import pandas as pd
import asyncio

class TimeoutException(Exception):
    pass

def handler(signum, frame):
    raise TimeoutException("Timeout: repair_cve took more than 10 minutes")

def run_pipeline(row):
    try:
        cve_id = row["cve_id"]
        commit_hash = row["commit_id"]
        repo_url = row["project"]
        repo_dir = f"../mh_sf_cve_repos/{cve_id}"
        cve_desc = row["cve_description"]
        
        signal.alarm(600)  # 10 minutes timeout for AutoGen
        agent = AutoGenRepairAgent(
            vuln_path=row["file_name"],
            host_repo_dir=repo_dir,
            cve_description=cve_desc,
            tools_allowed=['flawfinder'],  # AutoGen agent uses specific tools
            client_api_key=os.getenv('GEMINI_API_KEY'),
            base_url=os.getenv('GEMINI_BASE_URL'),
            model_name=os.getenv('GEMINI_MODEL'),
        )
        agent.setup_environment()
        asyncio.run(agent.run_repair_conversation())
        agent.extract_patch(cve_id, "cves_patches")
        agent.cleanup()
        return True

    except TimeoutException as te:
        print("Timeout:", f"{cve_id} - {str(te)}")
        return False
    except Exception as e:
        print("Error processing:", f"{cve_id} - {str(e)}")
        return False

# Example usage
if __name__ == '__main__':
    load_dotenv()
    signal.signal(signal.SIGALRM, handler)

    # Load the CSV file into a DataFrame
    test_df = pd.read_csv("mh_sf_reposvul.csv")
    failed_count = 0

    # Get unique CVE IDs to avoid duplicate processing
    unique_cves = test_df.drop_duplicates(subset=['cve_id'])
    progress_bar = tqdm(unique_cves.iterrows(), total=len(unique_cves), desc="Processing CVEs")

    for _, row in progress_bar:
        cve_id = row["cve_id"]
        success = run_pipeline(row)
        if not success:
            failed_count += 1
        progress_bar.set_postfix(failed=failed_count)