import os
import subprocess
import time
import json
from typing import List, Tuple, Optional, Dict, Any
import tempfile
from utils.function_replacer import FunctionReplacer
# from autogen_core.models import ChatCompletionClient
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage  # or your own message class
from autogen_core.model_context import BufferedChatCompletionContext
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import ModelInfo
from autogen_agentchat.conditions import TextMentionTermination, TimeoutTermination
from autogen_agentchat.teams import RoundRobinGroupChat
import asyncio

# Abstract tool base class
class Tool:
    """
    Abstract base class for tools available within the security agent.
    """
    name: str

    def run(self, *args: str) -> str:
        """
        Execute the tool with provided arguments and return combined stdout and stderr.
        """
        raise NotImplementedError

# Concrete tool implementations
class FlawfinderTool(Tool):
    name = 'flawfinder'
    def run(self, *args: str) -> str:
        cmd = ' '.join(['flawfinder'] + list(args))
        result = subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', cmd], capture_output=True, text=True)
        return result.stdout + result.stderr

SYSTEM_MESSAGE = (
    "You are a security-fix agent running within a Docker container.\n"
    "Your mission is to locate and fix a software vulnerability by iteratively reasoning and using the available tools.\n"
    "Your task is to fix the vulnerability inside functions of a code. You have access to a set of tools that can help you inspect and modify functions.\n"
    "\n"
    "Available tools:\n"
    "  - get_function_names: Get list of function names from the vulnerable code\n"
    "  - get_function_definition: Get the definition of a specific function from the vulnerable code\n"
    "  - replace_in_function: Replace specific strings within a function in the vulnerable code\n"
    "  - run_flawfinder: Run flawfinder static analysis security scanner on C/C++ code\n"
    "\n"
    "To modify functions in the vulnerable code, use the function-specific tools:\n"
    "- First use `get_function_names` to see what functions are available\n"
    "- Use `get_function_definition` to examine specific functions\n"
    "- Use `replace_in_function` to make targeted changes within functions\n"
    "- Use `run_flawfinder` to scan for security vulnerabilities in C/C++ code and validate fixes\n"
    "Goal:\n"
    "Based on the provided cve_description, locate the vulnerable functions and code segments, apply the minimal patch to the vulnerable code using the function tools. If you change a function, check if other functions that caller and callee functions are affected. Use flawfinder to scan for vulnerabilities after making changes to validate the fix.\n"
    "\n"
    "When you believe the vulnerability is fixed and validated, respond with 'VULNERABILITY_FIXED' to complete the task."
    "Always reflect on the tool outputs and decide on the next action."
    "\n"
    "Input:\n"
    "vuln_path: {vuln_path}\n"
    "cve_description: {cve_description}\n"
)

# Main agent class
class AutoGenRepairAgent:
    """
    An AutoGen-based agent for automated vulnerability patching inside a Docker container.

    Attributes:
        vuln_path (str): Absolute host path to the vulnerable code directory.
        host_repo_dir (str): Host repository directory.
        cve_description (str): Description of the vulnerability.
        tools_allowed (List[str]): Keys of allowed tools.
        model_choice (str): 'openai' or 'ollama'.
        client_api_key, base_url, model_name: optional config.
        container (str): Docker container name.
        container_root (str): Container root directory.
    """
    
    def __init__(
        self,
        vuln_path: str,
        host_repo_dir: str,
        cve_description: str,
        tools_allowed: List[str],
        client_api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        model_name: Optional[str] = None,
        container_root: str = '/workspace/vuln'
    ):
        self.vuln_path = vuln_path
        self.host_repo_dir = host_repo_dir
        self.cve_description = cve_description
        self.tools_allowed = tools_allowed
        self.client_api_key = client_api_key
        self.base_url = base_url
        self.model_name = model_name
        self.container = f"agent_env_{int(time.time())}"
        self.container_root = container_root
        self.modified_functions = set()
        self.num_iterations = 0
        self.tool_usage = {}

        # Initialize FunctionReplacer for code analysis and modification
        self.function_replacer = FunctionReplacer()

        # Initialize tool instances
        self.tool_instances: Dict[str, Tool] = {}
        self.tool_instances['flawfinder'] = FlawfinderTool()
        
        # Bind container to each tool
        for t in self.tool_instances.values(): 
            setattr(t, 'container', self.container)

        if not client_api_key or not base_url or not model_name:
            raise ValueError("openai requires client_api_key, base_url and model_name")
        
        # Initialize AutoGen agents
        self.setup_agents()

    def setup_agents(self):
        """Setup AutoGen assistant and user proxy agents."""
        # Create assistant agent
        self.system_message = SYSTEM_MESSAGE + f"\n\nVulnerable file: {self.vuln_path}\nCVE Description: {self.cve_description}"
        self.model_client = OpenAIChatCompletionClient(
            model=self.model_name,
            api_key=self.client_api_key,
            base_url=self.base_url,
            model_info=ModelInfo(vision=True, function_calling=True, json_output=True, family="unknown", structured_output=True)
        )
        self.model_context = BufferedChatCompletionContext(buffer_size=1000)
        self.tools = [self.get_function_names, self.get_function_definition, self.replace_in_function, self.run_flawfinder]
        max_msg_termination = TimeoutTermination(timeout_seconds=120)
        text_termination = TextMentionTermination("VULNERABILITY_FIXED")
        combined_termination = max_msg_termination | text_termination

        self.assistant = AssistantAgent(
            name="repair_agent",
            description="An agent that fixes vulnerabilities in code.",
            model_client=self.model_client,
            system_message=self.system_message,
            model_context=self.model_context,
            tools=self.tools,
            reflect_on_tool_use=True,
            model_client_stream=False
        )

        # Create a team with the looped assistant agent and the termination condition.
        self.team = RoundRobinGroupChat(
            [self.assistant],
            termination_condition=combined_termination,
        )
    
    async def run_repair_conversation(self):
        """Run the AutoGen conversation to repair the vulnerability."""
        initial_message = (
            # f"Please analyze and fix the vulnerability in the file '{self.vuln_path}'. "
            # f"CVE Description: {self.cve_description}\n\n"
            "Repair the vulnerability while following the system instructions."
            "Use get_function_names, get_function_definition, and replace_in_function to analyze functions and modify vulnerable ones."
            "Use flawfinder to validate fixes."
            # "When you believe the vulnerability is fixed and validated, respond with 'VULNERABILITY_FIXED'."
            "Start by using flawfinder to get more information about the vulnerability."
            "After that, get the list of functions, then examine the relevant functions."
            
        )
        async for message in self.team.run_stream(task=initial_message):  # type: ignore
            print(type(message).__name__)
        

    def setup_environment(self):
        """Setup Docker environment for the agent."""
        subprocess.run(['docker','run','-dit','--name',self.container,'python:3.11-slim'], check=True)
        subprocess.run(['docker', 'exec', self.container, 'mkdir', '-p', self.container_root], check=True)
        
        # Install required tools
        cmds = ['apt-get update && apt-get install -y git flawfinder']
        for c in cmds:
            subprocess.run(['docker','exec',self.container,'bash','-lc',c], check=True)
        
        # Setup directory structure and copy files
        rel_path = os.path.normpath(self.vuln_path)
        parent_dir = os.path.dirname(rel_path)
        if parent_dir:
            docker_target_dir = f'{self.container_root}/{parent_dir}'
            subprocess.run(['docker', 'exec', self.container, 'mkdir', '-p', docker_target_dir], check=True)

        # Copy the file or directory to the correct location
        host_full_path = os.path.join(self.host_repo_dir, self.vuln_path)
        container_target_path = f'{self.container}:{self.container_root}/{self.vuln_path}'
        subprocess.run(['docker', 'cp', host_full_path, container_target_path], check=True)

        # Initialize git repository
        init_cmds = [
            f'git -C {self.container_root} init',
            f'git -C {self.container_root} config user.email <EMAIL>',
            f'git -C {self.container_root} config user.name Agent',
            f'git -C {self.container_root} add -A',
            f'git -C {self.container_root} commit -m "baseline"',
        ]
        for c in init_cmds:
            subprocess.run(['docker','exec', self.container, 'bash', '-lc', c], check=True)

    # Tool implementation methods
    def get_function_names(self) -> str:
        """
        Get list of function names from the vulnerable code.
        
        Returns:
            A string containing the list of function names.
        """
        try:
            code_content = self._get_vuln_code_content()
            if code_content.startswith("Error"):
                return code_content

            function_names = self.function_replacer.get_function_names(code_content)
            if not function_names:
                return "No functions found in the vulnerable code file."

            self.tool_usage['get_function_names'] = self.tool_usage.get('get_function_names', 0) + 1
            return f"Functions found: {', '.join(function_names)}"
        except Exception as e:
            return f"Error getting function names: {e}"

    def get_function_definition(self, function_name: str) -> str:
        """
        Get the definition of a specific function from the vulnerable code.
        
        Arguments:
            function_name: The name of the function to retrieve
            
        Returns:
            The function definition as a string
        """
        try:
            code_content = self._get_vuln_code_content()
            if code_content.startswith("Error"):
                return code_content

            function_def = self.function_replacer.get_function_definition(code_content, function_name)
            self.tool_usage['get_function_definition'] = self.tool_usage.get('get_function_definition', 0) + 1
            return f"Function definition for '{function_name}':\n\n{function_def}"
        except ValueError as e:
            return f"Function '{function_name}' not found: {e}"
        except Exception as e:
            return f"Error getting function definition: {e}"

    def replace_in_function(self, function_name: str, replacements: List[Dict[str, str]]) -> str:
        """
        Replace specific strings within a function in the vulnerable code.
        
        Arguments:
            function_name: The name of the function to modify
            replacements: List of dicts with 'old' and 'new' keys for string replacements
            
        Returns:
            A string indicating the success of the operation.
        """
        try:
            code_content = self._get_vuln_code_content()
            if code_content.startswith("Error"):
                return code_content

            updated_code = self.function_replacer.replace_in_function(code_content, function_name, replacements)

            write_result = self._write_vuln_code_content(updated_code)
            if write_result.startswith("Error"):
                return write_result

            self.modified_functions.add(function_name)
            self.tool_usage['replace_in_function'] = self.tool_usage.get('replace_in_function', 0) + 1

            # Create summary of replacements
            summary = [f"Replacements applied to function '{function_name}':"]
            for i, replacement in enumerate(replacements, 1):
                old = replacement.get('old', '')
                new = replacement.get('new', '')
                summary.append(f"[{i}] '{old}' -> '{new}'")

            return "\n".join(summary) + f"\n\n{write_result}"
        except ValueError as e:
            return f"Function '{function_name}' not found: {e}"
        except Exception as e:
            return f"Error replacing in function: {e}"

    def run_flawfinder(self) -> str:
        """
        Run flawfinder tool with predefined arguments.

        Returns:
            The output of flawfinder as a string
        """
        try:
            self.tool_usage['flawfinder'] = self.tool_usage.get('flawfinder', 0) + 1
            # Run flawfinder with fixed arguments: --quiet, --dataonly, and the vulnerable path
            # return self.tool_instances['flawfinder'].run('--quiet', '--dataonly', f'{self.container_root}/{self.vuln_path}')
            return self.tool_instances['flawfinder'].run(f'{self.container_root}/{self.vuln_path}')
        except Exception as e:
            return f"Error running flawfinder: {e}"

    def _get_vuln_code_content(self) -> str:
        """Helper method to get the content of the vulnerable code file."""
        try:
            result = subprocess.run(
                ["docker", "exec", self.container, "cat", f"{self.container_root}/{self.vuln_path}"],
                capture_output=True, text=True, check=True
            )
            return result.stdout
        except subprocess.CalledProcessError as e:
            return f"Error reading vulnerable code file: {e}"

    def _write_vuln_code_content(self, content: str) -> str:
        """Helper method to write content back to the vulnerable code file."""
        try:
            tmpdir = tempfile.mkdtemp(prefix="vuln_code_")
            temp_filepath = os.path.join(tmpdir, os.path.basename(self.vuln_path))

            with open(temp_filepath, "w", encoding="utf-8") as f:
                f.write(content)

            subprocess.run(
                ["docker", "cp", temp_filepath, f"{self.container}:{self.container_root}/{self.vuln_path}"],
                check=True
            )

            # Stage the changes
            subprocess.run(
                ["docker", "exec", self.container, "bash", "-lc", f"git -C {self.container_root} add -A"],
                check=True
            )

            return "File updated successfully."
        except Exception as e:
            return f"Error writing vulnerable code file: {e}"

    def extract_patch(self, patch_name: str, host_dest: str) -> str:
        """Extract the patch and copy to host."""
        # Stage any changes made by the agent
        subprocess.run(['docker', 'exec', self.container, 'bash', '-lc', f'git -C {self.container_root} add -A'], check=True)

        # Ensure there are staged changes
        staged_check = subprocess.run(
            ['docker', 'exec', self.container, 'bash', '-lc', f'git -C {self.container_root} diff --cached --quiet || echo CHANGED'],
            capture_output=True, text=True, check=True
        )
        if 'CHANGED' not in staged_check.stdout:
            raise RuntimeError('No staged changes; file unchanged.')

        host_dest_folder = os.path.join(host_dest, patch_name)
        os.makedirs(host_dest_folder, exist_ok=True)
        
        # Copy the entire changed file to host
        host_file_path = os.path.join(host_dest_folder, os.path.basename(self.vuln_path))
        subprocess.run(['docker', 'cp', f'{self.container}:{self.container_root}/{self.vuln_path}', host_file_path], check=True)

        metadata = {
            "num_iterations": self.num_iterations,
            "tool_usage": self.tool_usage,
            "modified_functions": list(self.modified_functions)
        }
        metadata_path = os.path.join(host_dest_folder, f'{patch_name}_metadata.json')
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)

        return host_file_path

    def cleanup(self):
        """Clean up Docker container."""
        subprocess.run(['docker','rm','-f',self.container], check=True)
        self.model_context.clear()
