Create a new class representing a ReAct repair agent where you use the AssistantAgent from autogen. The class should receive the following init inputs:
def __init__(
        self,
        vuln_path: str,
        host_repo_dir: str,
        tools_allowed: List[str],
        model_choice: str = 'openai',
        client_api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        model_name: Optional[str] = None,
        container_root: str = '/workspace/vuln'
    )

It 



import os
from transformers import AutoModelForCausalLM, AutoTokenizer
# Export HF_TOKEN environment variable
os.environ["HF_TOKEN"] = "*************************************"
repo_id = 'meta-llama/CodeLlama-13b-Instruct-hf'

model = AutoModelForCausalLM.from_pretrained(
   repo_id, device_map="cuda:0", torch_dtype=torch.bfloat16, attn_implementation="flash_attention_2"
)
tokenizer = AutoTokenizer.from_pretrained(repo_id)
# print gpu usage with 